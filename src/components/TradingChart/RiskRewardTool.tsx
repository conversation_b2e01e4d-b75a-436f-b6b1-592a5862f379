import React from 'react';

interface RRData {
  entry: number;
  stop: number;
  target: number;
  direction: 'long' | 'short';
  positionSize?: number;
  timeStart: string;
  timeEnd: string;
}

interface RiskRewardToolProps {
  drawing: any;
  index: number;
  onEdit?: (index: number) => void;
  onDelete?: (index: number) => void;
  isDraggable?: boolean;
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void;
  onUpdateDrawing?: (index: number, updatedDrawing: any) => void;
}

// Helper functions for RR calculations
const calculateRiskReward = (rrData: RRData) => {
  const risk = Math.abs(rrData.entry - rrData.stop);
  const reward = Math.abs(rrData.target - rrData.entry);
  const rr = risk > 0 ? reward / risk : 0;

  const dollarRisk = rrData.positionSize ? rrData.positionSize * risk : null;
  const dollarReward = rrData.positionSize ? rrData.positionSize * reward : null;

  return { risk, reward, rr, dollarRisk, dollarReward };
};

const convertDrawingToRRData = (drawing: any): RRData | null => {
  if (!drawing.rrData) {
    // Legacy conversion from points-based structure
    if (!drawing.points || drawing.points.length < 3) return null;

    const [entryPoint, stopPoint, targetPoint] = drawing.points;
    const timeStart = new Date().toISOString();
    const timeEnd = new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(); // 6 hours later

    return {
      entry: entryPoint.price,
      stop: stopPoint.price,
      target: targetPoint.price,
      direction: targetPoint.price > entryPoint.price ? 'long' : 'short',
      positionSize: drawing.positionSize || undefined,
      timeStart,
      timeEnd
    };
  }

  return drawing.rrData;
};

export const createRiskRewardSeries = (
  drawing: any,
  index: number,
  onEdit?: (index: number) => void,
  isDraggable: boolean = true,
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void,
  onUpdateDrawing?: (index: number, updatedDrawing: any) => void
) => {
  const rrData = convertDrawingToRRData(drawing);
  if (!rrData) return [];

  const style = drawing.style || {};
  const { risk, reward, rr, dollarRisk, dollarReward } = calculateRiskReward(rrData);

  // Colors based on direction and zones
  const entryColor = style.entryColor || '#ffffff';
  const riskColor = style.riskColor || '#ff4757';
  const rewardColor = style.rewardColor || '#00e7b6';
  const lineWidth = style.width || 2;
  const showLabels = style.showLabels !== false;
  const showRatio = style.showRatio !== false;

  const series = [];

  // Time boundaries for the RR box
  const leftTime = rrData.timeStart;
  const rightTime = rrData.timeEnd;

  // 1. Draw the three horizontal lines (Stop, Entry, Target)

  // Entry line (neutral color)
  series.push({
    name: `RR_${index}_entry_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, rrData.entry], [rightTime, rrData.entry]],
    lineStyle: {
      color: entryColor,
      width: lineWidth + 1,
      type: 'solid'
    },
    symbol: 'none',
    z: 1003,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_entry_line'
  });

  // Stop loss line
  series.push({
    name: `RR_${index}_stop_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, rrData.stop], [rightTime, rrData.stop]],
    lineStyle: {
      color: riskColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1003,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_stop_line'
  });

  // Target line
  series.push({
    name: `RR_${index}_target_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, rrData.target], [rightTime, rrData.target]],
    lineStyle: {
      color: rewardColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1003,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_target_line'
  });

  // 2. Draw the colored boxes based on direction
  const minPrice = Math.min(rrData.entry, rrData.stop, rrData.target);
  const maxPrice = Math.max(rrData.entry, rrData.stop, rrData.target);

  // Vertical border lines
  series.push({
    name: `RR_${index}_left_border`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, minPrice], [leftTime, maxPrice]],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_border'
  });

  series.push({
    name: `RR_${index}_right_border`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[rightTime, minPrice], [rightTime, maxPrice]],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_border'
  });

  // 3. Colored fill areas based on direction
  if (rrData.direction === 'long') {
    // Long trade: Green box from entry to target (upward), Red box from entry to stop (downward)

    // Reward zone (green) - from entry to target
    const rewardTop = Math.max(rrData.entry, rrData.target);
    const rewardBottom = Math.min(rrData.entry, rrData.target);

    series.push({
      name: `RR_${index}_reward_fill`,
      type: 'custom',
      coordinateSystem: 'cartesian2d',
      renderItem: (params: any, api: any) => {
        const leftPoint = api.coord([leftTime, rewardTop]);
        const rightPoint = api.coord([rightTime, rewardBottom]);

        return {
          type: 'rect',
          shape: {
            x: leftPoint[0],
            y: leftPoint[1],
            width: rightPoint[0] - leftPoint[0],
            height: rightPoint[1] - leftPoint[1]
          },
          style: {
            fill: `${rewardColor}25`, // 15% opacity
            stroke: 'transparent'
          }
        };
      },
      data: [0],
      z: 1000,
      silent: false,
      animation: false,
      drawingIndex: index,
      drawingType: 'rr_reward_fill'
    });

    // Risk zone (red) - from entry to stop
    const riskTop = Math.max(rrData.entry, rrData.stop);
    const riskBottom = Math.min(rrData.entry, rrData.stop);

    series.push({
      name: `RR_${index}_risk_fill`,
      type: 'custom',
      coordinateSystem: 'cartesian2d',
      renderItem: (params: any, api: any) => {
        const leftPoint = api.coord([leftTime, riskTop]);
        const rightPoint = api.coord([rightTime, riskBottom]);

        return {
          type: 'rect',
          shape: {
            x: leftPoint[0],
            y: leftPoint[1],
            width: rightPoint[0] - leftPoint[0],
            height: rightPoint[1] - leftPoint[1]
          },
          style: {
            fill: `${riskColor}25`, // 15% opacity
            stroke: 'transparent'
          }
        };
      },
      data: [0],
      z: 1000,
      silent: false,
      animation: false,
      drawingIndex: index,
      drawingType: 'rr_risk_fill'
    });

  } else {
    // Short trade: Green box from entry to target (downward), Red box from entry to stop (upward)

    // Reward zone (green) - from entry to target (downward for short)
    const rewardTop = Math.max(rrData.entry, rrData.target);
    const rewardBottom = Math.min(rrData.entry, rrData.target);

    series.push({
      name: `RR_${index}_reward_fill`,
      type: 'custom',
      coordinateSystem: 'cartesian2d',
      renderItem: (params: any, api: any) => {
        const leftPoint = api.coord([leftTime, rewardTop]);
        const rightPoint = api.coord([rightTime, rewardBottom]);

        return {
          type: 'rect',
          shape: {
            x: leftPoint[0],
            y: leftPoint[1],
            width: rightPoint[0] - leftPoint[0],
            height: rightPoint[1] - leftPoint[1]
          },
          style: {
            fill: `${rewardColor}25`, // 15% opacity
            stroke: 'transparent'
          }
        };
      },
      data: [0],
      z: 1000,
      silent: false,
      animation: false,
      drawingIndex: index,
      drawingType: 'rr_reward_fill'
    });

    // Risk zone (red) - from entry to stop (upward for short)
    const riskTop = Math.max(rrData.entry, rrData.stop);
    const riskBottom = Math.min(rrData.entry, rrData.stop);

    series.push({
      name: `RR_${index}_risk_fill`,
      type: 'custom',
      coordinateSystem: 'cartesian2d',
      renderItem: (params: any, api: any) => {
        const leftPoint = api.coord([leftTime, riskTop]);
        const rightPoint = api.coord([rightTime, riskBottom]);

        return {
          type: 'rect',
          shape: {
            x: leftPoint[0],
            y: leftPoint[1],
            width: rightPoint[0] - leftPoint[0],
            height: rightPoint[1] - leftPoint[1]
          },
          style: {
            fill: `${riskColor}25`, // 15% opacity
            stroke: 'transparent'
          }
        };
      },
      data: [0],
      z: 1000,
      silent: false,
      animation: false,
      drawingIndex: index,
      drawingType: 'rr_risk_fill'
    });
  }

  // 4. Labels for each level
  if (showLabels) {
    const rightTimeMs = new Date(rightTime).getTime();
    const labelTime = new Date(rightTimeMs + (rightTimeMs - new Date(leftTime).getTime()) * 0.05).toISOString();

    // Entry label
    series.push({
      name: `RR_${index}_entry_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[labelTime, rrData.entry]],
      symbol: 'none',
      label: {
        show: true,
        formatter: `Entry: $${rrData.entry.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 11,
        backgroundColor: entryColor,
        borderRadius: 4,
        padding: [3, 6],
        position: 'right'
      },
      z: 1004,
      silent: true,
      animation: false
    });

    // Stop loss label
    series.push({
      name: `RR_${index}_stop_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[labelTime, rrData.stop]],
      symbol: 'none',
      label: {
        show: true,
        formatter: `Stop: $${rrData.stop.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 11,
        backgroundColor: riskColor,
        borderRadius: 4,
        padding: [3, 6],
        position: 'right'
      },
      z: 1004,
      silent: true,
      animation: false
    });

    // Target label
    series.push({
      name: `RR_${index}_target_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[labelTime, rrData.target]],
      symbol: 'none',
      label: {
        show: true,
        formatter: `Target: $${rrData.target.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 11,
        backgroundColor: rewardColor,
        borderRadius: 4,
        padding: [3, 6],
        position: 'right'
      },
      z: 1004,
      silent: true,
      animation: false
    });
  }

  // 5. Risk/Reward ratio and dollar amounts display
  if (showRatio) {
    const leftTimeMs = new Date(leftTime).getTime();
    const rightTimeMs = new Date(rightTime).getTime();
    const centerTime = new Date((leftTimeMs + rightTimeMs) / 2).toISOString();
    const centerPrice = (minPrice + maxPrice) / 2;

    // Main RR ratio
    const rrText = `R/R: 1:${rr.toFixed(2)}`;
    const dollarText = dollarRisk && dollarReward
      ? `\nRisk: $${dollarRisk.toFixed(0)} | Reward: $${dollarReward.toFixed(0)}`
      : '';

    series.push({
      name: `RR_${index}_ratio_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, centerPrice]],
      symbol: 'rect',
      symbolSize: dollarText ? [140, 40] : [100, 28],
      label: {
        show: true,
        formatter: rrText + dollarText,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 12,
        backgroundColor: 'rgba(0, 0, 0, 0.85)',
        borderColor: '#00e7b6',
        borderWidth: 1,
        borderRadius: 6,
        padding: [6, 10],
        position: 'inside'
      },
      itemStyle: {
        color: 'transparent',
        borderColor: '#00e7b6',
        borderWidth: 1
      },
      z: 1005,
      silent: true,
      animation: false
    });
  }

  // 6. Draggable handles for interactivity
  if (isDraggable) {
    const handleSize = 8;
    const leftTimeMs = new Date(leftTime).getTime();
    const rightTimeMs = new Date(rightTime).getTime();
    const centerTime = new Date((leftTimeMs + rightTimeMs) / 2).toISOString();

    // Corner resize handles for adjusting box width and height
    series.push({
      name: `RR_${index}_topleft_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[leftTime, maxPrice]],
      symbol: 'rect',
      symbolSize: handleSize,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'nw-resize',
      drawingIndex: index,
      handleType: 'resize',
      resizeCorner: 'topleft',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });

    series.push({
      name: `RR_${index}_topright_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, maxPrice]],
      symbol: 'rect',
      symbolSize: handleSize,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'ne-resize',
      drawingIndex: index,
      handleType: 'resize',
      resizeCorner: 'topright',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });

    series.push({
      name: `RR_${index}_bottomleft_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[leftTime, minPrice]],
      symbol: 'rect',
      symbolSize: handleSize,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'sw-resize',
      drawingIndex: index,
      handleType: 'resize',
      resizeCorner: 'bottomleft',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });

    series.push({
      name: `RR_${index}_bottomright_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, minPrice]],
      symbol: 'rect',
      symbolSize: handleSize,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'se-resize',
      drawingIndex: index,
      handleType: 'resize',
      resizeCorner: 'bottomright',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });

    // Individual price level handles
    // Entry level handle (moves all levels together)
    series.push({
      name: `RR_${index}_entry_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.entry]],
      symbol: 'circle',
      symbolSize: handleSize + 2,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex: index,
      handleType: 'entry',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Stop level handle (adjusts stop only)
    series.push({
      name: `RR_${index}_stop_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.stop]],
      symbol: 'circle',
      symbolSize: handleSize,
      itemStyle: {
        color: riskColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'ns-resize',
      drawingIndex: index,
      handleType: 'stop',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Target level handle (adjusts target only)
    series.push({
      name: `RR_${index}_target_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.target]],
      symbol: 'circle',
      symbolSize: handleSize,
      itemStyle: {
        color: rewardColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'ns-resize',
      drawingIndex: index,
      handleType: 'target',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });
  }

  return series;
};

// Helper function to create a new RR tool
export const createNewRRTool = (
  entryPrice: number,
  entryTime: string,
  direction: 'long' | 'short' = 'long',
  positionSize?: number
): any => {
  const stopOffset = direction === 'long' ? entryPrice * 0.02 : entryPrice * 0.02; // 2% default
  const targetOffset = direction === 'long' ? entryPrice * 0.06 : entryPrice * 0.06; // 6% default

  const stop = direction === 'long' ? entryPrice - stopOffset : entryPrice + stopOffset;
  const target = direction === 'long' ? entryPrice + targetOffset : entryPrice - targetOffset;

  const timeStart = entryTime;
  const timeEnd = new Date(new Date(entryTime).getTime() + 6 * 60 * 60 * 1000).toISOString(); // 6 hours later

  return {
    type: 'rr',
    id: Date.now(),
    rrData: {
      entry: entryPrice,
      stop,
      target,
      direction,
      positionSize,
      timeStart,
      timeEnd
    },
    style: {
      entryColor: '#ffffff',
      riskColor: '#ff4757',
      rewardColor: '#00e7b6',
      width: 2,
      showLabels: true,
      showRatio: true
    }
  };
};

// Helper function to update RR tool data
export const updateRRTool = (
  drawing: any,
  updates: Partial<RRData>
): any => {
  const currentRRData = convertDrawingToRRData(drawing);
  if (!currentRRData) return drawing;

  const updatedRRData = { ...currentRRData, ...updates };

  // Auto-detect direction if entry, stop, or target changed
  if (updates.entry !== undefined || updates.stop !== undefined || updates.target !== undefined) {
    updatedRRData.direction = updatedRRData.target > updatedRRData.entry ? 'long' : 'short';
  }

  return {
    ...drawing,
    rrData: updatedRRData
  };
};

// Helper function to handle drag updates for RR tools
export const handleRRDrag = (
  drawing: any,
  handleType: string,
  newPrice: number,
  newTime?: string
): any => {
  const rrData = convertDrawingToRRData(drawing);
  if (!rrData) return drawing;

  switch (handleType) {
    case 'entry':
      // Move all levels together (maintain relative distances)
      const entryDelta = newPrice - rrData.entry;
      return updateRRTool(drawing, {
        entry: newPrice,
        stop: rrData.stop + entryDelta,
        target: rrData.target + entryDelta
      });

    case 'stop':
      // Move only stop level
      return updateRRTool(drawing, { stop: newPrice });

    case 'target':
      // Move only target level
      return updateRRTool(drawing, { target: newPrice });

    case 'resize':
      // Handle box resizing (adjust time boundaries)
      if (newTime) {
        // This would need more complex logic based on which corner is being dragged
        // For now, just update the time boundaries
        return updateRRTool(drawing, {
          timeStart: newTime < rrData.timeEnd ? newTime : rrData.timeStart,
          timeEnd: newTime > rrData.timeStart ? newTime : rrData.timeEnd
        });
      }
      break;

    default:
      return drawing;
  }

  return drawing;
};

const RiskRewardTool: React.FC<RiskRewardToolProps> = ({ drawing, index, onEdit, onDelete }) => {
  // This component is mainly for type definitions and utility functions
  // The actual rendering is handled by createRiskRewardSeries function
  return null;
};

export default RiskRewardTool;
