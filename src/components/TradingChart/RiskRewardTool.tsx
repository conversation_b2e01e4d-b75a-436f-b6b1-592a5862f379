import React from 'react';

interface RiskRewardToolProps {
  drawing: any;
  index: number;
  onEdit?: (index: number) => void;
  onDelete?: (index: number) => void;
  isDraggable?: boolean;
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void;
}

export const createRiskRewardSeries = (
  drawing: any,
  index: number,
  onEdit?: (index: number) => void,
  isDraggable: boolean = true,
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void
) => {
  if (!drawing.points || drawing.points.length < 3) return [];

  const [entry, stop, target] = drawing.points;
  const style = drawing.style || {};

  // Colors with defaults
  const entryColor = style.entryColor || '#ffffff';
  const stopColor = style.stopColor || '#ff4757';
  const targetColor = style.targetColor || '#00e7b6';
  const lineWidth = style.width || 2;
  const showLabels = style.showLabels !== false;
  const showRatio = style.showRatio !== false;

  // Calculate risk/reward ratio
  const riskAmount = Math.abs(entry.price - stop.price);
  const rewardAmount = Math.abs(target.price - entry.price);
  const rrRatio = riskAmount > 0 ? (rewardAmount / riskAmount).toFixed(2) : '0.00';

  const series = [];

  // Ensure consistent time format
  const entryTime = typeof entry.time === 'string' ? entry.time : new Date(entry.time).toISOString();
  const stopTime = typeof stop.time === 'string' ? stop.time : new Date(stop.time).toISOString();
  const targetTime = typeof target.time === 'string' ? target.time : new Date(target.time).toISOString();

  // Main vertical line spanning all levels
  const minPrice = Math.min(entry.price, stop.price, target.price);
  const maxPrice = Math.max(entry.price, stop.price, target.price);

  // Use the entry time as the main vertical line position
  const mainLineTime = entryTime;

  series.push({
    name: `RR_${index}_main_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[mainLineTime, minPrice], [mainLineTime, maxPrice]],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_main'
  });

  // Horizontal level lines extending from each point
  const timeOffset = 1000 * 60 * 60 * 12; // 12 hour offset for horizontal lines
  const entryTimeMs = new Date(entryTime).getTime();
  const stopTimeMs = new Date(stopTime).getTime();
  const targetTimeMs = new Date(targetTime).getTime();

  // Entry level line (horizontal)
  series.push({
    name: `RR_${index}_entry_horizontal`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [
      [new Date(entryTimeMs - timeOffset).toISOString(), entry.price],
      [new Date(entryTimeMs + timeOffset).toISOString(), entry.price]
    ],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_entry_line'
  });

  // Stop loss level line (horizontal)
  series.push({
    name: `RR_${index}_stop_horizontal`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [
      [new Date(stopTimeMs - timeOffset).toISOString(), stop.price],
      [new Date(stopTimeMs + timeOffset).toISOString(), stop.price]
    ],
    lineStyle: {
      color: stopColor,
      width: lineWidth,
      type: 'dashed'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_stop_line'
  });

  // Take profit level line (horizontal)
  series.push({
    name: `RR_${index}_target_horizontal`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [
      [new Date(targetTimeMs - timeOffset).toISOString(), target.price],
      [new Date(targetTimeMs + timeOffset).toISOString(), target.price]
    ],
    lineStyle: {
      color: targetColor,
      width: lineWidth,
      type: 'dashed'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_target_line'
  });

  // Risk zone (red background) - using main line time
  const riskZoneData = [
    [mainLineTime, Math.min(entry.price, stop.price)],
    [mainLineTime, Math.max(entry.price, stop.price)]
  ];

  series.push({
    name: `RR_${index}_risk_zone`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: riskZoneData,
    lineStyle: {
      color: `${stopColor}60`, // 38% opacity for better visibility
      width: 25
    },
    symbol: 'none',
    z: 1001,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_risk_zone'
  });

  // Reward zone (green background) - using main line time
  const rewardZoneData = [
    [mainLineTime, Math.min(entry.price, target.price)],
    [mainLineTime, Math.max(entry.price, target.price)]
  ];

  series.push({
    name: `RR_${index}_reward_zone`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: rewardZoneData,
    lineStyle: {
      color: `${targetColor}60`, // 38% opacity for better visibility
      width: 25
    },
    symbol: 'none',
    z: 1001,
    silent: false,
    animation: false,
    drawingIndex: index,
    drawingType: 'rr_reward_zone'
  });

  if (showLabels) {
    // Entry label
    series.push({
      name: `RR_${index}_entry_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, entry.price]],
      symbol: 'circle',
      symbolSize: 8,
      label: {
        show: true,
        formatter: `Entry\n$${entry.price.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 10,
        backgroundColor: entryColor,
        borderRadius: 4,
        padding: [4, 8],
        position: 'right'
      },
      itemStyle: { color: entryColor },
      z: 1003,
      silent: false,
      animation: false
    });

    // Stop loss label
    series.push({
      name: `RR_${index}_stop_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, stop.price]],
      symbol: 'circle',
      symbolSize: 8,
      label: {
        show: true,
        formatter: `Stop\n$${stop.price.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 10,
        backgroundColor: stopColor,
        borderRadius: 4,
        padding: [4, 8],
        position: 'right'
      },
      itemStyle: { color: stopColor },
      z: 1003,
      silent: false,
      animation: false
    });

    // Take profit label
    series.push({
      name: `RR_${index}_target_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, target.price]],
      symbol: 'circle',
      symbolSize: 8,
      label: {
        show: true,
        formatter: `Target\n$${target.price.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 10,
        backgroundColor: targetColor,
        borderRadius: 4,
        padding: [4, 8],
        position: 'right'
      },
      itemStyle: { color: targetColor },
      z: 1003,
      silent: false,
      animation: false
    });
  }

  if (showRatio) {
    // Risk/Reward ratio label
    const midPrice = (Math.min(entry.price, stop.price, target.price) + Math.max(entry.price, stop.price, target.price)) / 2;
    
    series.push({
      name: `RR_${index}_ratio_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, midPrice]],
      symbol: 'rect',
      symbolSize: [60, 20],
      label: {
        show: true,
        formatter: `R/R: 1:${rrRatio}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 11,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00e7b6',
        borderWidth: 1,
        borderRadius: 4,
        padding: [4, 8],
        position: 'inside'
      },
      itemStyle: { 
        color: 'transparent',
        borderColor: '#00e7b6',
        borderWidth: 1
      },
      z: 1004,
      silent: false,
      animation: false
    });
  }

  // Draggable handles for each point (if dragging is enabled)
  if (isDraggable) {
    const handleSize = 8;

    // Entry handle - positioned at the actual entry point
    series.push({
      name: `RR_${index}_entry_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entryTime, entry.price]],
      symbol: 'circle',
      symbolSize: handleSize,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex: index,
      pointIndex: 0,
      drawingType: 'rr_handle',
      isDraggableHandle: true
    });

    // Stop handle - positioned at the actual stop point
    series.push({
      name: `RR_${index}_stop_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[stopTime, stop.price]],
      symbol: 'circle',
      symbolSize: handleSize,
      itemStyle: {
        color: stopColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex: index,
      pointIndex: 1,
      drawingType: 'rr_handle',
      isDraggableHandle: true
    });

    // Target handle - positioned at the actual target point
    series.push({
      name: `RR_${index}_target_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[targetTime, target.price]],
      symbol: 'circle',
      symbolSize: handleSize,
      itemStyle: {
        color: targetColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex: index,
      pointIndex: 2,
      drawingType: 'rr_handle',
      isDraggableHandle: true
    });
  }

  return series;
};

const RiskRewardTool: React.FC<RiskRewardToolProps> = ({ drawing, index, onEdit, onDelete }) => {
  // This component is mainly for type definitions and utility functions
  // The actual rendering is handled by createRiskRewardSeries function
  return null;
};

export default RiskRewardTool;
