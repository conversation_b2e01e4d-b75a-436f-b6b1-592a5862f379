import React from 'react';

interface RiskRewardToolProps {
  drawing: any;
  index: number;
  onEdit?: (index: number) => void;
  onDelete?: (index: number) => void;
}

export const createRiskRewardSeries = (drawing: any, index: number, onEdit?: (index: number) => void) => {
  if (!drawing.points || drawing.points.length < 3) return [];

  const [entry, stop, target] = drawing.points;
  const style = drawing.style || {};
  
  // Colors with defaults
  const entryColor = style.entryColor || '#ffffff';
  const stopColor = style.stopColor || '#ff4757';
  const targetColor = style.targetColor || '#00e7b6';
  const lineWidth = style.width || 2;
  const showLabels = style.showLabels !== false;
  const showRatio = style.showRatio !== false;

  // Calculate risk/reward ratio
  const riskAmount = Math.abs(entry.price - stop.price);
  const rewardAmount = Math.abs(target.price - entry.price);
  const rrRatio = riskAmount > 0 ? (rewardAmount / riskAmount).toFixed(2) : '0.00';

  const series = [];

  // Main vertical line spanning all levels
  const minPrice = Math.min(entry.price, stop.price, target.price);
  const maxPrice = Math.max(entry.price, stop.price, target.price);
  
  series.push({
    name: `RR_${index}_main_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[entry.time, minPrice], [entry.time, maxPrice]],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false
  });

  // Entry level line (horizontal)
  const timeOffset = 1000 * 60 * 60 * 24; // 1 day offset for horizontal lines
  const entryTime = new Date(entry.time).getTime();
  
  series.push({
    name: `RR_${index}_entry_horizontal`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [
      [new Date(entryTime - timeOffset).toISOString(), entry.price],
      [new Date(entryTime + timeOffset).toISOString(), entry.price]
    ],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false
  });

  // Stop loss level line (horizontal)
  series.push({
    name: `RR_${index}_stop_horizontal`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [
      [new Date(entryTime - timeOffset).toISOString(), stop.price],
      [new Date(entryTime + timeOffset).toISOString(), stop.price]
    ],
    lineStyle: {
      color: stopColor,
      width: lineWidth,
      type: 'dashed'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false
  });

  // Take profit level line (horizontal)
  series.push({
    name: `RR_${index}_target_horizontal`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [
      [new Date(entryTime - timeOffset).toISOString(), target.price],
      [new Date(entryTime + timeOffset).toISOString(), target.price]
    ],
    lineStyle: {
      color: targetColor,
      width: lineWidth,
      type: 'dashed'
    },
    symbol: 'none',
    z: 1002,
    silent: false,
    animation: false
  });

  // Risk zone (red background)
  const riskZoneData = [
    [entry.time, Math.min(entry.price, stop.price)],
    [entry.time, Math.max(entry.price, stop.price)]
  ];
  
  series.push({
    name: `RR_${index}_risk_zone`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: riskZoneData,
    lineStyle: {
      color: `${stopColor}40`, // 25% opacity
      width: 20
    },
    symbol: 'none',
    z: 1001,
    silent: false,
    animation: false
  });

  // Reward zone (green background)
  const rewardZoneData = [
    [entry.time, Math.min(entry.price, target.price)],
    [entry.time, Math.max(entry.price, target.price)]
  ];
  
  series.push({
    name: `RR_${index}_reward_zone`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: rewardZoneData,
    lineStyle: {
      color: `${targetColor}40`, // 25% opacity
      width: 20
    },
    symbol: 'none',
    z: 1001,
    silent: false,
    animation: false
  });

  if (showLabels) {
    // Entry label
    series.push({
      name: `RR_${index}_entry_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, entry.price]],
      symbol: 'circle',
      symbolSize: 8,
      label: {
        show: true,
        formatter: `Entry\n$${entry.price.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 10,
        backgroundColor: entryColor,
        borderRadius: 4,
        padding: [4, 8],
        position: 'right'
      },
      itemStyle: { color: entryColor },
      z: 1003,
      silent: false,
      animation: false
    });

    // Stop loss label
    series.push({
      name: `RR_${index}_stop_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, stop.price]],
      symbol: 'circle',
      symbolSize: 8,
      label: {
        show: true,
        formatter: `Stop\n$${stop.price.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 10,
        backgroundColor: stopColor,
        borderRadius: 4,
        padding: [4, 8],
        position: 'right'
      },
      itemStyle: { color: stopColor },
      z: 1003,
      silent: false,
      animation: false
    });

    // Take profit label
    series.push({
      name: `RR_${index}_target_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, target.price]],
      symbol: 'circle',
      symbolSize: 8,
      label: {
        show: true,
        formatter: `Target\n$${target.price.toFixed(2)}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 10,
        backgroundColor: targetColor,
        borderRadius: 4,
        padding: [4, 8],
        position: 'right'
      },
      itemStyle: { color: targetColor },
      z: 1003,
      silent: false,
      animation: false
    });
  }

  if (showRatio) {
    // Risk/Reward ratio label
    const midPrice = (Math.min(entry.price, stop.price, target.price) + Math.max(entry.price, stop.price, target.price)) / 2;
    
    series.push({
      name: `RR_${index}_ratio_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[entry.time, midPrice]],
      symbol: 'rect',
      symbolSize: [60, 20],
      label: {
        show: true,
        formatter: `R/R: 1:${rrRatio}`,
        color: '#ffffff',
        fontWeight: 'bold',
        fontSize: 11,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        borderColor: '#00e7b6',
        borderWidth: 1,
        borderRadius: 4,
        padding: [4, 8],
        position: 'inside'
      },
      itemStyle: { 
        color: 'transparent',
        borderColor: '#00e7b6',
        borderWidth: 1
      },
      z: 1004,
      silent: false,
      animation: false
    });
  }

  // Draggable handles for each point
  const handleSize = 6;
  
  // Entry handle
  series.push({
    name: `RR_${index}_entry_handle`,
    type: 'scatter',
    coordinateSystem: 'cartesian2d',
    data: [[entry.time, entry.price]],
    symbol: 'circle',
    symbolSize: handleSize,
    itemStyle: {
      color: entryColor,
      borderColor: '#ffffff',
      borderWidth: 2
    },
    z: 1005,
    silent: false,
    animation: false,
    cursor: 'move'
  });

  // Stop handle
  series.push({
    name: `RR_${index}_stop_handle`,
    type: 'scatter',
    coordinateSystem: 'cartesian2d',
    data: [[entry.time, stop.price]],
    symbol: 'circle',
    symbolSize: handleSize,
    itemStyle: {
      color: stopColor,
      borderColor: '#ffffff',
      borderWidth: 2
    },
    z: 1005,
    silent: false,
    animation: false,
    cursor: 'move'
  });

  // Target handle
  series.push({
    name: `RR_${index}_target_handle`,
    type: 'scatter',
    coordinateSystem: 'cartesian2d',
    data: [[entry.time, target.price]],
    symbol: 'circle',
    symbolSize: handleSize,
    itemStyle: {
      color: targetColor,
      borderColor: '#ffffff',
      borderWidth: 2
    },
    z: 1005,
    silent: false,
    animation: false,
    cursor: 'move'
  });

  return series;
};

const RiskRewardTool: React.FC<RiskRewardToolProps> = ({ drawing, index, onEdit, onDelete }) => {
  // This component is mainly for type definitions and utility functions
  // The actual rendering is handled by createRiskRewardSeries function
  return null;
};

export default RiskRewardTool;
