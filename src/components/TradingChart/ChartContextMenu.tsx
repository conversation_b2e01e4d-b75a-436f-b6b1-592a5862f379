import React from 'react';
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuSeparator, ContextMenuSub, ContextMenuSubContent, ContextMenuSubTrigger } from '@/components/ui/context-menu';

interface ChartContextMenuProps {
  isOpen: boolean;
  position: { x: number; y: number } | null;
  onClose: () => void;
  onResetView: () => void;
  onFitToData: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onToggleGrid: () => void;
  onToggleCrosshair: () => void;
  onOpenSettings: () => void;
  onSelectTool: (toolId: string) => void;
  onClearAllDrawings: () => void;
  showGrid: boolean;
  showCrosshair: boolean;
  currentTool: string | null;
}

const ChartContextMenu: React.FC<ChartContextMenuProps> = ({
  isOpen,
  position,
  onClose,
  onResetView,
  onFitToData,
  onZoomIn,
  onZoomOut,
  onToggleGrid,
  onToggleCrosshair,
  onOpenSettings,
  onSelectTool,
  onClearAllDrawings,
  showGrid,
  showCrosshair,
  currentTool
}) => {
  if (!isOpen || !position) return null;

  const drawingTools = [
    { id: 'cursor', name: 'Cursor', icon: '↖️' },
    { id: 'line', name: 'Trend Line', icon: '📈' },
    { id: 'rr', name: 'Risk/Reward', icon: '⚖️' },
    { id: 'rectangle', name: 'Rectangle', icon: '⬜' },
    { id: 'circle', name: 'Circle', icon: '⭕' },
    { id: 'fib', name: 'Fibonacci', icon: '🌀' },
    { id: 'measure', name: 'Measure', icon: '📏' }
  ];

  return (
    <div
      className="fixed inset-0 z-[9999]"
      onClick={onClose}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div
        className="absolute bg-[#1a1a1a] border border-white/[0.12] rounded-lg shadow-2xl min-w-[200px] py-2"
        style={{
          left: position.x,
          top: position.y,
          transform: 'translate(0, 0)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* View Controls */}
        <div className="px-3 py-1 text-xs font-medium text-white/60 uppercase tracking-wider">
          View
        </div>
        
        <button
          className="w-full px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
          onClick={() => {
            onResetView();
            onClose();
          }}
        >
          <span className="text-sm">🔄</span>
          Reset View
        </button>
        
        <button
          className="w-full px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
          onClick={() => {
            onFitToData();
            onClose();
          }}
        >
          <span className="text-sm">📊</span>
          Fit to Data
        </button>

        <div className="flex">
          <button
            className="flex-1 px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
            onClick={() => {
              onZoomIn();
              onClose();
            }}
          >
            <span className="text-sm">🔍</span>
            Zoom In
          </button>
          
          <button
            className="flex-1 px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
            onClick={() => {
              onZoomOut();
              onClose();
            }}
          >
            <span className="text-sm">🔍</span>
            Zoom Out
          </button>
        </div>

        <div className="h-px bg-white/[0.12] my-2" />

        {/* Display Options */}
        <div className="px-3 py-1 text-xs font-medium text-white/60 uppercase tracking-wider">
          Display
        </div>
        
        <button
          className="w-full px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
          onClick={() => {
            onToggleGrid();
            onClose();
          }}
        >
          <span className="text-sm">{showGrid ? '✅' : '⬜'}</span>
          Grid
        </button>
        
        <button
          className="w-full px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
          onClick={() => {
            onToggleCrosshair();
            onClose();
          }}
        >
          <span className="text-sm">{showCrosshair ? '✅' : '⬜'}</span>
          Crosshair
        </button>

        <div className="h-px bg-white/[0.12] my-2" />

        {/* Drawing Tools */}
        <div className="px-3 py-1 text-xs font-medium text-white/60 uppercase tracking-wider">
          Drawing Tools
        </div>
        
        {drawingTools.map((tool) => (
          <button
            key={tool.id}
            className={`w-full px-4 py-2 text-left hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3 ${
              currentTool === tool.id ? 'bg-[#00e7b6]/20 text-[#00e7b6]' : 'text-white/80'
            }`}
            onClick={() => {
              onSelectTool(tool.id);
              onClose();
            }}
          >
            <span className="text-sm">{tool.icon}</span>
            {tool.name}
            {currentTool === tool.id && <span className="ml-auto text-xs">●</span>}
          </button>
        ))}

        <div className="h-px bg-white/[0.12] my-2" />

        {/* Actions */}
        <div className="px-3 py-1 text-xs font-medium text-white/60 uppercase tracking-wider">
          Actions
        </div>
        
        <button
          className="w-full px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
          onClick={() => {
            onClearAllDrawings();
            onClose();
          }}
        >
          <span className="text-sm">🗑️</span>
          Clear All Drawings
        </button>
        
        <button
          className="w-full px-4 py-2 text-left text-white/80 hover:bg-white/[0.08] hover:text-white transition-colors flex items-center gap-3"
          onClick={() => {
            onOpenSettings();
            onClose();
          }}
        >
          <span className="text-sm">⚙️</span>
          Chart Settings
        </button>
      </div>
    </div>
  );
};

export default ChartContextMenu;
